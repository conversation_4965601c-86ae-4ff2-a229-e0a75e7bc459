import { useState } from 'react'
import Background3D from './Background3D'

function ProfilePage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const teamMembers = [
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Co-Founder & CEO",
      image: "/profile_pictures/Aryaman.png",
      link: "https://www.linkedin.com/in/aryaman-9282141b8",
      description: "Visionary leader driving AI innovation and strategic business growth, transforming complex challenges into intelligent solutions."
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Co-Founder & CTO",
      image: "/profile_pictures/Harsh.png",
      link: "https://harsh-pandey-portfolio.netlify.app/",
      description: "Technical architect and full-stack developer building cutting-edge AI systems with expertise in machine learning and scalable infrastructure."
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Co-Founder & CPO",
      image: "/profile_pictures/Subhanshu.png",
      link: "https://www.linkedin.com/in/subhanshu-arya-3a7017186",
      description: "Product strategist and AI researcher crafting user-centric experiences, bridging advanced technology with practical market solutions."
    }
  ]

  return (
    <div className="min-h-screen w-full relative">
      {/* Three.js Background */}
      <Background3D />

      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm sticky top-0 z-50 w-full relative">
        <nav className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <a href="/" className="text-3xl font-extrabold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent hover:scale-105 transition-transform duration-300 cursor-pointer tracking-tight">
                Qreate AI
              </a>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              <a href="/" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                Home
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="/#portfolio" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                Portfolio
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="/#services" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                Services
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="/#faqs" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                FAQs
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="https://calendly.com/qareailabs" target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group font-medium text-base tracking-tight">
                Contact Us
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-300 hover:text-white focus:outline-none focus:text-white transition-colors duration-300"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden animate-fade-in">
              <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800/50 backdrop-blur-sm rounded-lg">
                <a href="/" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Home</a>
                <a href="/#portfolio" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Portfolio</a>
                <a href="/#services" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Services</a>
                <a href="/#faqs" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">FAQs</a>
                <a href="https://calendly.com/qareailabs" target="_blank" rel="noopener noreferrer" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Contact Us</a>
              </div>
            </div>
          )}
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative py-24 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"></div>
        <div className="relative max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-extrabold text-white mb-8 tracking-tight animate-fade-in-up">
            About Us
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-12 font-semibold animate-fade-in-up delay-200">
            The visionaries behind Qreate AI's innovative solutions
          </p>
        </div>
      </section>

      {/* About Us Section */}
      <section className="py-24 relative">
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-12 tracking-tight animate-fade-in-up">
            Our Mission
          </h2>
          <div className="space-y-8 animate-fade-in-up delay-200">
            <p className="text-lg md:text-xl text-gray-300 leading-relaxed">
              At Qreate AI, we're passionate about transforming complex challenges into intelligent solutions. Our team combines cutting-edge artificial intelligence research with practical engineering expertise to deliver innovative AI systems that make a real-world impact.
            </p>
            <p className="text-lg md:text-xl text-gray-300 leading-relaxed">
              From computer vision and reinforcement learning to environmental forecasting and quantum neural networks, we push the boundaries of what's possible with AI technology. Our diverse portfolio spans multiple industries, always with a focus on creating solutions that are both technically advanced and practically valuable.
            </p>
            <p className="text-lg md:text-xl text-gray-300 leading-relaxed">
              Founded by three visionary leaders with complementary expertise in business strategy, technical architecture, and product development, Qreate AI represents the perfect fusion of academic rigor and entrepreneurial innovation.
            </p>
          </div>
        </div>
      </section>

      {/* Team Profiles Section */}
      <section className="py-24 relative">
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"></div>
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-6 tracking-tight">Meet Our Team</h2>
            <p className="text-xl md:text-2xl text-gray-300 mb-4 font-semibold">Leadership & Innovation</p>
          </div>
          <div className="grid lg:grid-cols-3 gap-12">
            {teamMembers.map((member, index) => (
              <div key={member.name} className="profile-card group bg-gradient-to-br from-gray-800/30 to-gray-900/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-blue-500/50 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20 animate-fade-in-up" style={{ animationDelay: `${index * 200}ms` }}>
                <div className="profile-image relative aspect-square overflow-hidden">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                </div>
                <div className="p-8">
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300 tracking-tight">
                    {member.name}
                  </h3>
                  <p className="text-blue-400 text-lg font-semibold mb-4">
                    {member.role}
                  </p>
                  <p className="text-gray-300 text-base leading-relaxed mb-6">
                    {member.description}
                  </p>
                  <a
                    href={member.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-blue-400 hover:text-blue-300 text-sm font-medium transition-all duration-300 hover:scale-105 group"
                  >
                    <span>View Profile</span>
                    <svg className="w-3 h-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 backdrop-blur-sm py-12 relative">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4">Qreate AI</h3>
            <p className="text-gray-400 mb-6">Transforming ideas into intelligent solutions</p>
            <div className="flex justify-center space-x-6">
              <a href="https://calendly.com/qareailabs" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 relative group">
                Contact Us
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="/" className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 relative group">
                Home
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-700">
              <p className="text-gray-500 text-sm">© 2024 Qreate AI. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default ProfilePage
